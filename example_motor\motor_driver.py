"""
MaixCAM Pro 步进电机驱动库
基于原始树莓派PICO代码移植
支持双电机控制，通过单个串口通信，使用地址区分电机
"""

import time
from maix import uart, gpio
import struct


class MotorDriver:
    """步进电机驱动类"""

    def __init__(self, device="/dev/ttyS0", baudrate=115200, led_pin=None):
        """
        初始化电机驱动

        Args:
            device: 串口设备路径，默认使用UART0 (/dev/ttyS0)
            baudrate: 波特率，默认115200
            led_pin: LED指示灯引脚，如果为None则不使用LED
        """
        # 初始化LED灯（可选）
        self.led_pin = None
        if led_pin is not None:
            self.led_pin = gpio.GPIO(led_pin, gpio.Mode.OUT)
            self.led_pin.value(0)

        # 初始化串口 - 使用MaixPy的UART库
        self.serial = uart.UART(device, baudrate)
        
        # 功能码对照表
        self.func_codes = {
            'S_VER': 0x1F,      # 读取固件版本和对应的硬件版本
            'S_RL': 0x20,       # 读取读取相电阻和相电感
            'S_PID': 0x21,      # 读取PID参数
            'S_VBUS': 0x24,     # 读取总线电压
            'S_CPHA': 0x27,     # 读取相电流
            'S_ENCL': 0x31,     # 读取经过线性化校准后的编码器值
            'S_TPOS': 0x33,     # 读取电机目标位置角度
            'S_VEL': 0x35,      # 读取电机实时转速
            'S_CPOS': 0x36,     # 读取电机实时位置角度
            'S_PERR': 0x37,     # 读取电机位置误差角度
            'S_FLAG': 0x3A,     # 读取使能/到位/堵转状态标志位
            'S_ORG': 0x3B,      # 读取正在回零/回零失败状态标志位
            'S_Conf': 0x42,     # 读取驱动参数，功能码后面还需要加上一个辅助码0x6C
            'S_State': 0x43     # 读取系统状态参数，功能码后面还需要加上一个辅助码0x7A
        }
    
    def read_sys_params(self, addr, param_type):
        """
        读取驱动板参数
        
        Args:
            addr: 电机地址
            param_type: 参数类型 (如 'S_VER', 'S_CPOS' 等)
        """
        cmd = bytearray(16)
        i = 0
        cmd[i] = addr
        i += 1
        
        if param_type in self.func_codes:
            cmd[i] = self.func_codes[param_type]
            i += 1
        
        cmd[i] = 0x6B
        i += 1

        self.serial.write(bytes(cmd[:i]))
    
    def reset_current_position(self, addr):
        """
        将当前位置清零
        
        Args:
            addr: 电机地址
        """
        cmd = bytearray(4)
        cmd[0] = addr       # 地址
        cmd[1] = 0x0A       # 功能码
        cmd[2] = 0x6D       # 辅助码
        cmd[3] = 0x6B       # 校验字节
        
        self.serial.write(bytes(cmd))
    
    def reset_clog_protection(self, addr):
        """
        解除堵转保护
        
        Args:
            addr: 电机地址
        """
        cmd = bytearray(4)
        cmd[0] = addr       # 地址
        cmd[1] = 0x0E       # 功能码
        cmd[2] = 0x52       # 辅助码
        cmd[3] = 0x6B       # 校验字节
        
        self.serial.write(bytes(cmd))
    
    def modify_control_mode(self, addr, save_flag, ctrl_mode):
        """
        修改控制模式
        
        Args:
            addr: 电机地址
            save_flag: 是否存储标志 (True/False)
            ctrl_mode: 控制模式
        """
        cmd = bytearray(6)
        cmd[0] = addr                           # 地址
        cmd[1] = 0x46                           # 功能码
        cmd[2] = 0x69                           # 辅助码
        cmd[3] = 0x01 if save_flag else 0x00    # 是否存储标志
        cmd[4] = ctrl_mode                      # 控制模式
        cmd[5] = 0x6B                           # 校验字节
        
        self.serial.write(bytes(cmd))

    def enable_motor(self, addr, state, sync_flag):
        """
        电机使能控制

        Args:
            addr: 电机地址
            state: 使能状态 (True/False)
            sync_flag: 多机同步标志 (True/False)
        """
        cmd = bytearray(6)
        cmd[0] = addr                           # 地址
        cmd[1] = 0xF3                           # 功能码
        cmd[2] = 0xAB                           # 辅助码
        cmd[3] = 0x01 if state else 0x00        # 使能状态
        cmd[4] = 0x01 if sync_flag else 0x00    # 多机同步运动标志
        cmd[5] = 0x6B                           # 校验字节

        self.serial.write(bytes(cmd))

    def velocity_control(self, addr, direction, velocity, acceleration, sync_flag):
        """
        速度控制

        Args:
            addr: 电机地址
            direction: 方向 (0为CW，其他为CCW)
            velocity: 速度 (RPM)
            acceleration: 加速度
            sync_flag: 多机同步标志 (True/False)
        """
        cmd = bytearray(8)
        cmd[0] = addr                               # 地址
        cmd[1] = 0xF6                               # 功能码
        cmd[2] = direction                          # 方向
        cmd[3] = (velocity >> 8) & 0xFF             # 速度高8位
        cmd[4] = velocity & 0xFF                    # 速度低8位
        cmd[5] = acceleration                       # 加速度
        cmd[6] = 0x01 if sync_flag else 0x00        # 多机同步标志
        cmd[7] = 0x6B                               # 校验字节

        self.serial.write(bytes(cmd))
    
    def position_control(self, addr, direction, velocity, acceleration, pulses, relative_flag, sync_flag):
        """
        位置控制
        
        Args:
            addr: 电机地址
            direction: 方向
            velocity: 速度 (RPM)
            acceleration: 加速度
            pulses: 脉冲数
            relative_flag: 相对/绝对标志 (True为相对，False为绝对)
            sync_flag: 多机同步标志 (True/False)
        """
        cmd = bytearray(13)
        cmd[0] = addr                               # 地址
        cmd[1] = 0xFD                               # 功能码
        cmd[2] = direction                          # 方向
        cmd[3] = (velocity >> 8) & 0xFF             # 速度高8位
        cmd[4] = velocity & 0xFF                    # 速度低8位
        cmd[5] = acceleration                       # 加速度
        cmd[6] = (pulses >> 24) & 0xFF              # 脉冲数最高8位
        cmd[7] = (pulses >> 16) & 0xFF              # 脉冲数次高8位
        cmd[8] = (pulses >> 8) & 0xFF               # 脉冲数次低8位
        cmd[9] = pulses & 0xFF                      # 脉冲数最低8位
        cmd[10] = 0x01 if relative_flag else 0x00   # 相对/绝对标志
        cmd[11] = 0x01 if sync_flag else 0x00       # 多机同步标志
        cmd[12] = 0x6B                              # 校验字节
        
        self.serial.write(bytes(cmd))

    def stop_motor(self, addr, sync_flag):
        """
        立即停止电机

        Args:
            addr: 电机地址
            sync_flag: 多机同步标志 (True/False)
        """
        cmd = bytearray(5)
        cmd[0] = addr                           # 地址
        cmd[1] = 0xFE                           # 功能码
        cmd[2] = 0x98                           # 辅助码
        cmd[3] = 0x01 if sync_flag else 0x00    # 多机同步标志
        cmd[4] = 0x6B                           # 校验字节

        self.serial.write(bytes(cmd))

    def synchronous_motion(self, addr):
        """
        执行多机同步运动命令

        Args:
            addr: 电机地址
        """
        cmd = bytearray(4)
        cmd[0] = addr       # 地址
        cmd[1] = 0xFF       # 功能码
        cmd[2] = 0x66       # 辅助码
        cmd[3] = 0x6B       # 校验字节

        self.serial.write(bytes(cmd))

    def set_origin_zero(self, addr, save_flag):
        """
        设置单圈回零零点位置

        Args:
            addr: 电机地址
            save_flag: 是否存储标志 (True/False)
        """
        cmd = bytearray(5)
        cmd[0] = addr                           # 地址
        cmd[1] = 0x93                           # 功能码
        cmd[2] = 0x88                           # 辅助码
        cmd[3] = 0x01 if save_flag else 0x00    # 是否存储标志
        cmd[4] = 0x6B                           # 校验字节

        self.serial.write(bytes(cmd))

    def modify_origin_params(self, addr, save_flag, o_mode, o_dir, o_vel, o_tm,
                           sl_vel, sl_ma, sl_ms, power_on_flag):
        """
        修改回零参数

        Args:
            addr: 电机地址
            save_flag: 是否存储标志
            o_mode: 回零模式
            o_dir: 回零方向
            o_vel: 回零速度
            o_tm: 回零超时时间
            sl_vel: 无限位碰撞检测转速
            sl_ma: 无限位碰撞检测电流
            sl_ms: 无限位碰撞检测时间
            power_on_flag: 上电自动触发回零
        """
        cmd = bytearray(20)
        cmd[0] = addr                                   # 地址
        cmd[1] = 0x4C                                   # 功能码
        cmd[2] = 0xAE                                   # 辅助码
        cmd[3] = 0x01 if save_flag else 0x00            # 是否存储标志
        cmd[4] = o_mode                                 # 回零模式
        cmd[5] = o_dir                                  # 回零方向
        cmd[6] = (o_vel >> 8) & 0xFF                    # 回零速度高8位
        cmd[7] = o_vel & 0xFF                           # 回零速度低8位
        cmd[8] = (o_tm >> 24) & 0xFF                    # 回零超时时间高8位
        cmd[9] = (o_tm >> 16) & 0xFF                    # 回零超时时间次高8位
        cmd[10] = (o_tm >> 8) & 0xFF                    # 回零超时时间次低8位
        cmd[11] = o_tm & 0xFF                           # 回零超时时间低8位
        cmd[12] = (sl_vel >> 8) & 0xFF                  # 无限位碰撞检测转速高8位
        cmd[13] = sl_vel & 0xFF                         # 无限位碰撞检测转速低8位
        cmd[14] = (sl_ma >> 8) & 0xFF                   # 无限位碰撞检测电流高8位
        cmd[15] = sl_ma & 0xFF                          # 无限位碰撞检测电流低8位
        cmd[16] = (sl_ms >> 8) & 0xFF                   # 无限位碰撞检测时间高8位
        cmd[17] = sl_ms & 0xFF                          # 无限位碰撞检测时间低8位
        cmd[18] = 0x01 if power_on_flag else 0x00       # 上电自动触发回零
        cmd[19] = 0x6B                                  # 校验字节

        self.serial.write(bytes(cmd))

    def trigger_origin_return(self, addr, o_mode, sync_flag):
        """
        触发回零

        Args:
            addr: 电机地址
            o_mode: 回零模式
            sync_flag: 多机同步标志 (True/False)
        """
        cmd = bytearray(5)
        cmd[0] = addr                           # 地址
        cmd[1] = 0x9A                           # 功能码
        cmd[2] = o_mode                         # 回零模式
        cmd[3] = 0x01 if sync_flag else 0x00    # 多机同步标志
        cmd[4] = 0x6B                           # 校验字节

        self.serial.write(bytes(cmd))

    def interrupt_origin(self, addr):
        """
        强制中断退出回零

        Args:
            addr: 电机地址
        """
        cmd = bytearray(4)
        cmd[0] = addr       # 地址
        cmd[1] = 0x9C       # 功能码
        cmd[2] = 0x48       # 辅助码
        cmd[3] = 0x6B       # 校验字节

        self.serial.write(bytes(cmd))

    def receive_data(self, timeout_ms=100):
        """
        接收串口数据

        Args:
            timeout_ms: 超时时间（毫秒）

        Returns:
            tuple: (hex_data, data_length)
        """
        # 使用MaixPy的UART读取方法
        data = self.serial.read(timeout=timeout_ms)

        if data:
            hex_data = ' '.join(['{:02x}'.format(b) for b in data])
            hex_data = hex_data.strip('00 ')
            if hex_data and hex_data[0] != '0':
                hex_data = '0' + hex_data
            return hex_data, len(data)
        else:
            return "", 0

    def get_real_time_position(self):
        """
        获取实时位置

        Returns:
            tuple: (motor1_position, motor2_position) 角度值
        """
        motor_cur_pos1 = 0.0
        motor_cur_pos2 = 0.0

        # 读取电机1实时位置
        self.read_sys_params(1, 'S_CPOS')
        data1, count1 = self.receive_data(200)  # 等待200ms

        if data1 and count1 > 0:
            data1_hex = data1.split()
            if len(data1_hex) >= 7 and int(data1_hex[0], 16) == 0x01 and int(data1_hex[1], 16) == 0x36:
                # 拼接成uint32_t类型
                pos1 = struct.unpack('>I', bytes.fromhex(''.join(data1_hex[3:7])))[0]
                # 角度转换
                motor_cur_pos1 = float(pos1) * 360.0 / 65536.0
                if int(data1_hex[2], 16):
                    motor_cur_pos1 = -motor_cur_pos1

        time.sleep(0.01)  # 短暂延时10ms

        # 读取电机2实时位置
        self.read_sys_params(2, 'S_CPOS')
        data2, count2 = self.receive_data(200)  # 等待200ms

        if data2 and count2 > 0:
            data2_hex = data2.split()
            if len(data2_hex) >= 7 and int(data2_hex[0], 16) == 0x02 and int(data2_hex[1], 16) == 0x36:
                # 拼接成uint32_t类型
                pos2 = struct.unpack('>I', bytes.fromhex(''.join(data2_hex[3:7])))[0]
                # 角度转换
                motor_cur_pos2 = float(pos2) * 360.0 / 65536.0
                if int(data2_hex[2], 16):
                    motor_cur_pos2 = -motor_cur_pos2

        return motor_cur_pos1, motor_cur_pos2

    def set_led(self, state):
        """
        设置LED状态

        Args:
            state: LED状态 (True/False)
        """
        if self.led_pin is not None:
            self.led_pin.value(1 if state else 0)

    def print_position(self):
        """
        打印当前位置信息（调试用）
        """
        motor1_pos, motor2_pos = self.get_real_time_position()
        print('Motor1: {:.1f}, Motor2: {:.1f}'.format(motor1_pos, motor2_pos))
        time.sleep(0.001)  # 1ms延时
