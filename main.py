'''
步进电机驱动测试程序
用于测试电机驱动协议和基本控制功能
1号电机控制水平方向旋转（Roll轴）
2号电机控制竖直方向旋转（Pitch轴）
'''

from maix import time, uart

class MotorDriver:
    def __init__(self):
        print("初始化电机驱动系统...")

        # 电机参数
        self.motor_speed = 150      # 电机转速 (RPM)
        self.motor_acceleration = 40 # 电机加速度
        self.angle_to_pulse = 3200 / 360  # 角度到脉冲转换比例

        # 初始化标志
        self.is_initialized = False

        # 初始化串口通信
        self.serial = uart.UART("/dev/ttyS0", 115200)

        # 初始化电机
        self.init_motors()

        print("电机驱动系统初始化完成")



    def send_motor_command(self, cmd):
        """发送电机控制命令"""
        self.serial.write(bytes(cmd))

    def modify_control_mode(self, addr, save_flag, ctrl_mode):
        """修改电机控制模式"""
        cmd = bytearray(6)
        cmd[0] = addr                           # 地址
        cmd[1] = 0x46                           # 功能码
        cmd[2] = 0x69                           # 辅助码
        cmd[3] = 0x01 if save_flag else 0x00    # 是否存储标志
        cmd[4] = ctrl_mode                      # 控制模式
        cmd[5] = 0x6B                           # 校验字节
        self.send_motor_command(cmd)

    def reset_current_position(self, addr):
        """将当前位置清零"""
        cmd = bytearray(4)
        cmd[0] = addr       # 地址
        cmd[1] = 0x0A       # 功能码
        cmd[2] = 0x6D       # 辅助码
        cmd[3] = 0x6B       # 校验字节
        self.send_motor_command(cmd)

    def enable_motor(self, addr, state, sync_flag):
        """电机使能控制"""
        cmd = bytearray(6)
        cmd[0] = addr                           # 地址
        cmd[1] = 0xF3                           # 功能码
        cmd[2] = 0xAB                           # 辅助码
        cmd[3] = 0x01 if state else 0x00        # 使能状态
        cmd[4] = 0x01 if sync_flag else 0x00    # 多机同步运动标志
        cmd[5] = 0x6B                           # 校验字节
        self.send_motor_command(cmd)

    def position_control(self, addr, direction, velocity, acceleration, pulses, relative_flag, sync_flag):
        """位置控制"""
        cmd = bytearray(13)
        cmd[0] = addr                               # 地址
        cmd[1] = 0xFD                               # 功能码
        cmd[2] = direction                          # 方向
        cmd[3] = (velocity >> 8) & 0xFF             # 速度高8位
        cmd[4] = velocity & 0xFF                    # 速度低8位
        cmd[5] = acceleration                       # 加速度
        cmd[6] = (pulses >> 24) & 0xFF              # 脉冲数最高8位
        cmd[7] = (pulses >> 16) & 0xFF              # 脉冲数次高8位
        cmd[8] = (pulses >> 8) & 0xFF               # 脉冲数次低8位
        cmd[9] = pulses & 0xFF                      # 脉冲数最低8位
        cmd[10] = 0x01 if relative_flag else 0x00   # 相对/绝对标志
        cmd[11] = 0x01 if sync_flag else 0x00       # 多机同步标志
        cmd[12] = 0x6B                              # 校验字节
        self.send_motor_command(cmd)

    def stop_motor(self, addr, sync_flag):
        """立即停止电机"""
        cmd = bytearray(5)
        cmd[0] = addr                           # 地址
        cmd[1] = 0xFE                           # 功能码
        cmd[2] = 0x98                           # 辅助码
        cmd[3] = 0x01 if sync_flag else 0x00    # 多机同步标志
        cmd[4] = 0x6B                           # 校验字节
        self.send_motor_command(cmd)

    def init_motors(self):
        """初始化电机"""
        print("初始化电机...")

        # 设置位置控制模式
        print("设置电机控制模式...")
        self.modify_control_mode(1, True, 3)  # 电机1 - Roll轴
        time.sleep(0.2)
        self.modify_control_mode(2, True, 3)  # 电机2 - Pitch轴
        time.sleep(0.2)

        # 设置零点
        print("设置电机零点...")
        self.reset_current_position(1)
        time.sleep(0.2)
        self.reset_current_position(2)
        time.sleep(0.2)

        # 启用电机
        print("启用电机...")
        self.enable_motor(1, True, False)
        time.sleep(0.2)
        self.enable_motor(2, True, False)
        time.sleep(0.2)

        self.is_initialized = True
        print("电机初始化完成")

    def control_motor_angle(self, motor_id, angle):
        """控制电机转动到指定角度"""
        # 限制角度范围
        angle = max(-180, min(180, angle))

        # 转换为脉冲数
        pulses = int(abs(angle) * self.angle_to_pulse)
        direction = 0 if angle >= 0 else 1

        print(f"控制电机{motor_id}转动到{angle}度，脉冲数：{pulses}")

        # 发送位置控制命令
        self.position_control(
            addr=motor_id,
            direction=direction,
            velocity=self.motor_speed,
            acceleration=self.motor_acceleration,
            pulses=pulses,
            relative_flag=False,
            sync_flag=False
        )

    def stop(self):
        """停止电机"""
        print("停止电机...")
        self.stop_motor(1, False)
        self.stop_motor(2, False)
        print("电机已停止")

def main():
    """主程序"""
    try:
        # 初始化电机驱动
        motor_driver = MotorDriver()

        print("开始电机测试...")

        # 测试电机1（Roll轴）转动30度
        print("测试电机1转动30度...")
        motor_driver.control_motor_angle(1, 30)
        time.sleep(2)

        # 测试电机2（Pitch轴）转动-20度
        # print("测试电机2转动-20度...")
        # motor_driver.control_motor_angle(2, -20)
        # time.sleep(2)

        # # 回到零位
        # print("电机回到零位...")
        # motor_driver.control_motor_angle(1, 0)
        # time.sleep(2)
        # motor_driver.control_motor_angle(2, 0)
        # time.sleep(2)

        print("电机测试完成")

    except Exception as e:
        print(f"程序运行错误: {e}")
        if 'motor_driver' in locals():
            motor_driver.stop()

if __name__ == "__main__":
    main()