'''
自稳步进云台控制系统
集成电机驱动协议和自稳定控制功能
1号电机控制水平方向旋转（Roll轴）
2号电机控制竖直方向旋转（Pitch轴）
通过IMU数据实现云台自稳定
'''

from maix import ahrs, time, uart
from maix.ext_dev import imu
import struct

class StabilizedGimbal:
    def __init__(self):
        print("初始化自稳云台系统...")

        # 控制参数
        self.stabilization_gain = 0.8  # 稳定增益
        self.max_correction_angle = 30  # 最大修正角度
        self.update_interval = 0.05    # 更新间隔50ms

        # 目标角度（水平状态）
        self.target_pitch = 0.0
        self.target_roll = 0.0

        # 角度滤波
        self.pitch_filter = 0.0
        self.roll_filter = 0.0
        self.filter_alpha = 0.7  # 滤波系数

        # 电机参数
        self.motor_speed = 150      # 电机转速 (RPM)
        self.motor_acceleration = 40 # 电机加速度
        self.angle_to_pulse = 65536.0 / 360.0  # 角度到脉冲转换比例

        # 初始化标志
        self.is_initialized = False
        self.stabilization_enabled = False

        # 初始化串口通信
        self.serial = uart.UART("/dev/ttyS0", 115200)

        # 初始化IMU和电机
        self.init_imu()
        self.init_motors()

        print("云台系统初始化完成")

    def init_imu(self):
        """初始化IMU传感器"""
        print("初始化IMU传感器...")

        self.sensor = imu.IMU("qmi8658", mode=imu.Mode.DUAL,
                             acc_scale=imu.AccScale.ACC_SCALE_2G,
                             acc_odr=imu.AccOdr.ACC_ODR_1000,
                             gyro_scale=imu.GyroScale.GYRO_SCALE_256DPS,
                             gyro_odr=imu.GyroOdr.GYRO_ODR_8000)

        self.ahrs_filter = ahrs.MahonyAHRS(2.0, 0.01)

        # 校准陀螺仪
        if not self.sensor.calib_gyro_exists():
            print("校准陀螺仪，请保持设备静止10秒...")
            self.sensor.calib_gyro(10000)
            print("陀螺仪校准完成")
        else:
            self.sensor.load_calib_gyro()
            print("加载陀螺仪校准数据")

    def send_motor_command(self, cmd):
        """发送电机控制命令"""
        self.serial.write(bytes(cmd))

    def modify_control_mode(self, addr, save_flag, ctrl_mode):
        """修改电机控制模式"""
        cmd = bytearray(6)
        cmd[0] = addr                           # 地址
        cmd[1] = 0x46                           # 功能码
        cmd[2] = 0x69                           # 辅助码
        cmd[3] = 0x01 if save_flag else 0x00    # 是否存储标志
        cmd[4] = ctrl_mode                      # 控制模式
        cmd[5] = 0x6B                           # 校验字节
        self.send_motor_command(cmd)

    def reset_current_position(self, addr):
        """将当前位置清零"""
        cmd = bytearray(4)
        cmd[0] = addr       # 地址
        cmd[1] = 0x0A       # 功能码
        cmd[2] = 0x6D       # 辅助码
        cmd[3] = 0x6B       # 校验字节
        self.send_motor_command(cmd)

    def enable_motor(self, addr, state, sync_flag):
        """电机使能控制"""
        cmd = bytearray(6)
        cmd[0] = addr                           # 地址
        cmd[1] = 0xF3                           # 功能码
        cmd[2] = 0xAB                           # 辅助码
        cmd[3] = 0x01 if state else 0x00        # 使能状态
        cmd[4] = 0x01 if sync_flag else 0x00    # 多机同步运动标志
        cmd[5] = 0x6B                           # 校验字节
        self.send_motor_command(cmd)

    def position_control(self, addr, direction, velocity, acceleration, pulses, relative_flag, sync_flag):
        """位置控制"""
        cmd = bytearray(13)
        cmd[0] = addr                               # 地址
        cmd[1] = 0xFD                               # 功能码
        cmd[2] = direction                          # 方向
        cmd[3] = (velocity >> 8) & 0xFF             # 速度高8位
        cmd[4] = velocity & 0xFF                    # 速度低8位
        cmd[5] = acceleration                       # 加速度
        cmd[6] = (pulses >> 24) & 0xFF              # 脉冲数最高8位
        cmd[7] = (pulses >> 16) & 0xFF              # 脉冲数次高8位
        cmd[8] = (pulses >> 8) & 0xFF               # 脉冲数次低8位
        cmd[9] = pulses & 0xFF                      # 脉冲数最低8位
        cmd[10] = 0x01 if relative_flag else 0x00   # 相对/绝对标志
        cmd[11] = 0x01 if sync_flag else 0x00       # 多机同步标志
        cmd[12] = 0x6B                              # 校验字节
        self.send_motor_command(cmd)

    def stop_motor(self, addr, sync_flag):
        """立即停止电机"""
        cmd = bytearray(5)
        cmd[0] = addr                           # 地址
        cmd[1] = 0xFE                           # 功能码
        cmd[2] = 0x98                           # 辅助码
        cmd[3] = 0x01 if sync_flag else 0x00    # 多机同步标志
        cmd[4] = 0x6B                           # 校验字节
        self.send_motor_command(cmd)

    def init_motors(self):
        """初始化电机"""
        print("初始化电机...")

        # 设置位置控制模式
        print("设置电机控制模式...")
        self.modify_control_mode(1, True, 3)  # 电机1 - Roll轴
        time.sleep(0.2)
        self.modify_control_mode(2, True, 3)  # 电机2 - Pitch轴
        time.sleep(0.2)

        # 设置零点
        print("设置电机零点...")
        self.reset_current_position(1)
        time.sleep(0.2)
        self.reset_current_position(2)
        time.sleep(0.2)

        # 启用电机
        print("启用电机...")
        self.enable_motor(1, True, False)
        time.sleep(0.2)
        self.enable_motor(2, True, False)
        time.sleep(0.2)

        self.is_initialized = True
        print("电机初始化完成")

    def get_filtered_angles(self, dt):
        """获取滤波后的角度"""
        # 读取IMU数据
        data = self.sensor.read_all(calib_gryo=True, radian=True)

        # 计算姿态角度
        angle = self.ahrs_filter.get_angle(data.acc, data.gyro, data.mag, dt, radian=False)

        # 低通滤波
        self.pitch_filter = self.filter_alpha * self.pitch_filter + (1 - self.filter_alpha) * angle.x
        self.roll_filter = self.filter_alpha * self.roll_filter + (1 - self.filter_alpha) * angle.y

        return self.pitch_filter, self.roll_filter, angle.z

    def calculate_correction(self, current_angle, target_angle):
        """计算修正角度"""
        error = target_angle - current_angle
        correction = error * self.stabilization_gain

        # 限制修正角度范围
        correction = max(-self.max_correction_angle, min(self.max_correction_angle, correction))

        return correction

    def control_motor_smooth(self, motor_id, target_angle):
        """平滑控制电机到目标角度"""
        # 限制角度范围
        target_angle = max(-45, min(45, target_angle))

        # 转换为脉冲数
        pulses = int(abs(target_angle) * self.angle_to_pulse)
        direction = 0 if target_angle >= 0 else 1

        # 发送位置控制命令
        self.position_control(
            addr=motor_id,
            direction=direction,
            velocity=self.motor_speed,
            acceleration=self.motor_acceleration,
            pulses=pulses,
            relative_flag=False,
            sync_flag=False
        )

    def enable_stabilization(self):
        """启用自稳定功能"""
        if self.is_initialized:
            self.stabilization_enabled = True
            print("自稳定功能已启用")
        else:
            print("请先初始化电机")

    def disable_stabilization(self):
        """禁用自稳定功能"""
        self.stabilization_enabled = False
        print("自稳定功能已禁用")

    def calibrate_level(self):
        """校准水平位置"""
        print("校准水平位置...")
        print("开始校准，请保持云台水平...")

        # 读取当前角度作为目标角度
        last_time = time.ticks_ms() / 1000.0
        pitch_sum = 0
        roll_sum = 0
        samples = 50

        for i in range(samples):
            current_time = time.ticks_ms() / 1000.0
            dt = current_time - last_time
            last_time = current_time

            pitch, roll, _ = self.get_filtered_angles(dt)
            pitch_sum += pitch
            roll_sum += roll

            time.sleep(0.02)

        self.target_pitch = pitch_sum / samples
        self.target_roll = roll_sum / samples

        print(f"水平位置已校准: Pitch={self.target_pitch:.2f}°, Roll={self.target_roll:.2f}°")

    def run_stabilization(self):
        """运行自稳定控制"""
        print("开始自稳定控制...")
        print("系统稳定中，等待3秒...")
        time.sleep(3)

        # 校准水平位置
        self.calibrate_level()

        # 启用自稳定
        self.enable_stabilization()

        last_time = time.ticks_ms() / 1000.0
        loop_count = 0

        print("自稳定已启动，按Ctrl+C停止")
        print("格式: 时间 | Pitch | Roll | Yaw | 修正Pitch | 修正Roll | 状态")

        try:
            while True:
                current_time = time.ticks_ms() / 1000.0
                dt = current_time - last_time
                last_time = current_time

                # 获取滤波后的角度
                pitch, roll, yaw = self.get_filtered_angles(dt)

                if self.stabilization_enabled:
                    # 计算修正角度
                    pitch_correction = self.calculate_correction(pitch, self.target_pitch)
                    roll_correction = self.calculate_correction(roll, self.target_roll)

                    # 控制电机
                    self.control_motor_smooth(2, pitch_correction)  # 电机2控制Pitch
                    self.control_motor_smooth(1, roll_correction)   # 电机1控制Roll
                else:
                    pitch_correction = 0
                    roll_correction = 0

                # 每秒打印10次状态
                if loop_count % 5 == 0:
                    status = "启用" if self.stabilization_enabled else "禁用"
                    print(f"{current_time:6.1f} | {pitch:6.2f} | {roll:6.2f} | {yaw:6.2f} | "
                          f"{pitch_correction:6.2f} | {roll_correction:6.2f} | {status}")

                loop_count += 1
                time.sleep(self.update_interval)

        except KeyboardInterrupt:
            print("\n正在停止自稳定...")
            self.stop()

    def stop(self):
        """停止云台"""
        print("停止电机...")
        self.disable_stabilization()
        self.stop_motor(1, False)
        self.stop_motor(2, False)
        print("云台已停止")

def main():
    """主程序"""
    try:
        gimbal = StabilizedGimbal()
        gimbal.run_stabilization()

    except Exception as e:
        print(f"程序运行错误: {e}")
        if 'gimbal' in locals():
            gimbal.stop()

if __name__ == "__main__":
    main()