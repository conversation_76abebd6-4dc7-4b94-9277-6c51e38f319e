# 自稳步进云台控制系统

这是一个基于MaixCAM Pro的自稳步进云台控制系统，使用IMU传感器实现云台的自动稳定功能。

## 系统组成

- **硬件**: MaixCAM Pro + IMU传感器(qmi8658) + 两个步进电机
- **软件**: MaixPy + 集成电机驱动协议

## 文件说明

### 主要程序文件

1. **main.py** - 自稳云台控制程序 ⭐ **主程序**
   - 集成了电机驱动协议和自稳定控制功能
   - 包含完整的初始化、控制和安全保护
   - 简洁高效，易于理解和修改

2. **motor_driver.py** - 独立电机驱动库（参考用）
   - 完整的电机驱动功能封装
   - 可用于其他项目

3. **motor_contral.py** - 原始参考代码
   - 树莓派PICO版本的原始代码

## 快速开始

### 1. 硬件连接

```
MaixCAM Pro UART0 ↔ 步进电机驱动板
- 电机1: 控制水平方向旋转 (Roll轴)
- 电机2: 控制竖直方向旋转 (Pitch轴)
- IMU传感器: 安装在云台末端
```

### 2. 运行云台程序

直接运行主程序：

```bash
python main.py
```

## 程序功能特点

### 自动初始化流程

1. **IMU校准**: 自动校准陀螺仪偏差
2. **电机设置**: 设置位置控制模式
3. **零点设置**: 将当前位置设为零点
4. **电机启用**: 启用两个电机

### 自稳定控制

- **实时角度检测**: 通过IMU获取云台姿态
- **误差计算**: 计算当前角度与目标角度的偏差
- **控制输出**: 通过PID算法计算电机修正角度
- **平滑控制**: 使用适当的速度和加速度控制电机

### 安全保护

- **角度限制**: 限制电机运动范围，防止过度旋转
- **平滑控制**: 使用较低的速度和加速度，避免突然运动
- **异常处理**: 捕获异常并安全停止电机

## 参数调节

### main.py 控制参数

```python
self.stabilization_gain = 0.8    # 稳定增益 (0.1-2.0)
self.max_correction_angle = 30   # 最大修正角度 (10-45度)
self.filter_alpha = 0.7          # 角度滤波系数 (0.1-0.9)
self.motor_speed = 150           # 电机转速 (50-300 RPM)
self.motor_acceleration = 40     # 电机加速度 (20-100)
```

## 调试建议

### 1. 参数调节顺序

1. 先确保硬件连接正常
2. 运行程序观察初始化过程
3. 根据实际效果调整参数

### 2. 参数调节

- **增益过小**: 响应慢，稳定效果差
- **增益过大**: 震荡，不稳定
- **建议**: 从小参数开始，逐步增加

### 3. 常见问题

**问题**: 电机不动
- 检查串口连接
- 确认电机地址设置
- 检查电源供应

**问题**: 云台震荡
- 降低控制增益
- 增加滤波系数
- 检查机械结构

**问题**: 响应慢
- 增加控制增益
- 减少更新间隔
- 优化PID参数

## 系统架构

```
IMU传感器 → AHRS滤波 → 角度计算 → PID控制 → 电机驱动 → 云台运动
    ↑                                                      ↓
    └─────────────── 反馈控制环路 ←─────────────────────────┘
```

## 注意事项

1. **上电顺序**: 先连接硬件，再运行程序
2. **校准环境**: IMU校准时保持设备完全静止
3. **机械限位**: 确保云台机械结构不会卡死
4. **电源稳定**: 确保电机驱动板电源稳定
5. **安全操作**: 测试时注意云台运动范围，避免碰撞

## 扩展功能

- 添加遥控功能
- 实现目标跟踪
- 增加多轴控制
- 集成相机稳定
- 添加手动控制模式

## 程序特点

### 🎯 一体化设计
- 电机驱动协议直接集成在主程序中
- 无需额外的驱动库文件
- 代码结构清晰，易于理解和修改

### 🚀 自动化流程
- 上电自动初始化电机零点
- 自动校准IMU陀螺仪
- 自动校准水平位置
- 一键启动自稳定功能

### 🛡️ 安全保护
- 角度限制防止过度旋转
- 平滑控制避免突然运动
- 异常处理自动停止电机

## 技术支持

如果您在使用过程中遇到问题，请检查：

1. 硬件连接是否正确
2. 电源供应是否稳定
3. 程序参数是否合适
4. IMU校准是否成功

程序已经过优化，集成了所有必要功能，直接运行main.py即可。
